#!/bin/bash
set -eux

# Service name
_storageFilename="${STORAGE_DIR}/${APP_NAME}"

# Remove cache
rm -rf ${REPOSITORY_NAME} ${_storageFilename}

# Prepare repository
git config --global url."https://$<EMAIL>/thrift-technology/".insteadOf "https://github.com/thrift-technology/"
git clone https://github.com/thrift-technology/${APP_NAME}.git ${REPOSITORY_NAME}
cp "${REPOSITORY_NAME}/app.config.sample.json" "${REPOSITORY_NAME}/app.config.json"

# Provisioning
cd ${REPOSITORY_NAME}
/bin/bash .scripts/provisioning.sh

# Build application
yarn cache clean
yarn install --immutable || yarn install --no-immutable
yarn build
cd ..

# Check tsconfig.json
_tsconfigFilename="${REPOSITORY_NAME}/tsconfig.json"
[ ! -f "$_tsconfigFilename" ] && echo "[ERROR] '${_tsconfigFilename}' folder not found" && exit 1
_builtDir=$(node -e "console.log(require('./${_tsconfigFilename}').compilerOptions.outDir)")

# Install production dependencies
rm -rf ${REPOSITORY_NAME}/node_modules
cd ${REPOSITORY_NAME} && yarn install --production --immutable && cd ..

# Copy files to service directory
mkdir -p ${_storageFilename}
cp -r ${REPOSITORY_NAME}/node_modules ${_storageFilename}/node_modules
cp -r ${REPOSITORY_NAME}/${_builtDir} ${_storageFilename}/${_builtDir}

# Copy credentials if exists
_count_pem_files=$(find "${REPOSITORY_NAME}/.credentials" -maxdepth 1 -type f -name "*.pem" | wc -l)
[ "$_count_pem_files" -gt 0 ] && cp -r ${REPOSITORY_NAME}/.credentials/*.pem ${_storageFilename}/${_builtDir}/.credentials

exit 0
